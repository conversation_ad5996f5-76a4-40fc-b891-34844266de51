import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useData } from '../data-provider';
import { useEffect, useState } from 'react';
import { useGet } from '@/hooks/useGet';
import { differenceInDays, format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatTimeWithText, secondsToTime } from '@/lib/utils';

type IBusyHistory = {
  id: number;
  staffId: number;
  busyAt: string;
  busyEndAt: string;
  busyTime: number;
};

export default function BusyHistory() {
  const { staffs, date } = useData();
  const [open, setOpen] = useState(false);
  const [busyHistory, setBusyHistory] = useState<IBusyHistory[]>([]);

  const { data: bhData } = useGet({
    url: '/staff/busy/history',
    enabled: open,
    params: {
      //   staffId: staff.id,
      dateOffset: differenceInDays(new Date(), date),
    },
  });

  useEffect(() => {
    if (bhData) {
      setBusyHistory(bhData);
    }
  }, [bhData]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="relative">
          Lịch sử bận rộn
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Lịch sử bận rộn</DialogTitle>
        </DialogHeader>
        <DialogDescription></DialogDescription>

        <div className="w-full max-h-[80vh] overflow-y-auto">
          <div className="overflow-x-auto">
            <Table className="min-w-[600px] text-sm md:text-base">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[70px]">STT</TableHead>
                  <TableHead>Họ và tên</TableHead>
                  <TableHead>Bắt đầu</TableHead>
                  <TableHead>Kết thúc</TableHead>
                  <TableHead>Thời gian</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {busyHistory.map((el, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{index + 1}</TableCell>
                    <TableCell>{staffs.find((item) => item.id == el.staffId)?.name}</TableCell>
                    <TableCell>{format(new Date(el.busyAt), 'HH:mm:ss')}</TableCell>
                    <TableCell>{format(new Date(el.busyEndAt), 'HH:mm:ss')}</TableCell>
                    <TableCell>{formatTimeWithText(secondsToTime(el.busyTime, false))}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
