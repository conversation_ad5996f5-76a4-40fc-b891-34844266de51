import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Menu } from 'lucide-react';
import { ModeToggle } from './mode-toggle';
import { Button } from './ui/button';
import { today } from '@/lib/utils';
import { menu, Sidebar } from './side-bar';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useShop } from '@/hooks/useShop';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export function NavBar() {
  const [open, setOpen] = useState(false);
  const { shops, shop, setShop } = useShop();

  const location = useLocation();

  useEffect(() => {
    const menuItem = menu.find((el) => el.path == location.pathname);
    document.title = menuItem?.label
      ? import.meta.env.VITE_TITLE + ' - ' + menuItem.label
      : import.meta.env.VITE_TITLE;
  }, [location]);

  // function fullScreen() {
  //   if (document.fullscreenElement) {
  //     document
  //       .exitFullscreen()
  //       // .then(() => console.log("Document Exited from Full screen mode"))
  //       .catch((err) => console.error(err));
  //   } else {
  //     document.documentElement.requestFullscreen();
  //   }
  // }

  return (
    <nav className="sticky top-0 z-50 glass border-b backdrop-blur-md">
      <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14 sm:h-16">
          <div className="flex items-center space-x-4">
            <Sheet open={open} onOpenChange={(value) => setOpen(value)}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="hover-lift">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="overflow-y-scroll border-r-0">
                <div className="h-full flex flex-col justify-between">
                  <div>
                    <SheetTitle className="text-xl font-bold bg-clip-text">
                      {import.meta.env.VITE_TITLE}
                    </SheetTitle>
                    <SheetDescription className="text-muted-foreground mt-2">
                      Hệ thống quản lý hiện đại
                    </SheetDescription>
                    <Sidebar setOpen={setOpen} />
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            <Select
              value={shop ? shop.id.toString() : ''}
              onValueChange={(value) =>
                setShop(shops.find((s) => s.id.toString() == value) ?? shops[0])
              }
            >
              <SelectTrigger className="max-w-[70vw] md:max-w-[300px] text-md md:text-xl font-bold">
                <SelectValue placeholder="Chọn cửa hàng" />
              </SelectTrigger>
              <SelectContent>
                {shops.map((s) => (
                  <SelectItem key={s.id} value={s.id.toString()}>
                    <div className="flex items-center">
                      {s.logoUrl && (
                        <img src={s.logoUrl} alt={s.name} className="w-6 h-6 mr-2 rounded" />
                      )}
                      <div className="truncate">{s.name}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-4">
            <div className="hidden md:block text-xs sm:text-sm font-medium text-muted-foreground">
              {today()}
            </div>
            <ModeToggle />
          </div>
        </div>
      </div>
    </nav>
  );
}
