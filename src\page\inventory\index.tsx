import './inventory.css';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { Loading } from '@/components/loading';

import { useEffect, useState } from 'react';
import { useProducts, useProductTypes } from '@/hooks/useInventory';
// import { AddProduct } from './crud/add-product';
// import { EditProduct } from './crud/edit-product';
// import { CreateTransaction, TransactionType } from './crud/create-transaction';
// import { Button } from '@/components/ui/button';
// import { Import } from 'lucide-react';
// import { DeleteProduct } from './crud/delete-product';
import { HistoryProduct } from './crud/history-product';
// import { AddProductType } from "./crud/add-product-type";
// import { DeleteProductType } from "./crud/delete-product-type";
// import { EditProductType } from "./crud/edit-product-type";

function capitalize(str: string) {
  if (!str) return '';
  str = str.trim();
  return str[0].toUpperCase() + str.slice(1).toLowerCase();
}

export function Inventory() {
  const [tab, setTab] = useState<string>();
  const [products, setProducts] = useState<IProduct[]>([]);
  const [unitFilter, setUnitFilter] = useState<string>('-1');
  const [productTypes, setProductTypes] = useState<IProductType[]>([]);

  const { isPending, data: pData, refetch } = useProducts();
  const { data: ptData } = useProductTypes();

  useEffect(() => {
    if (pData && pData.length > 0) {
      setProducts(pData);
    }
  }, [pData]);

  useEffect(() => {
    if (ptData && ptData.length >= 0) {
      setProductTypes(ptData);
    }
  }, [ptData]);

  useEffect(() => {
    if (productTypes) {
      if (productTypes.length == 0) {
        setTab(undefined);
        return;
      }

      if (!tab) {
        setTab(productTypes[0].id.toString());
        return;
      }

      const _staffType = productTypes.find((el) => el.id.toString() == tab);
      if (!_staffType) {
        setTab(productTypes[0].id.toString());
      }
    }
  }, [productTypes]);

  useEffect(() => {
    setUnitFilter('-1');
  }, [tab]);

  // const selectedProductType = useMemo(() => {
  //   return productTypes.find((el) => el.id.toString() === tab);
  // }, [productTypes, tab]);

  if (isPending) return <Loading />;

  return (
    <div>
      <Tabs className="mb-2" value={tab} onValueChange={(value) => setTab(value)}>
        <div className="flex justify-between items-center">
          <div>
            <TabsList>
              {productTypes.map((el) => (
                <TabsTrigger key={el.id} value={el.id.toString()}>
                  {el.name}
                </TabsTrigger>
              ))}
              {/* <AddProductType refetch={typeRefetch} /> */}
            </TabsList>
          </div>

          {/* <div>
            {selectedProductType && (
              <>
                <EditProductType
                  productType={selectedProductType}
                  refetch={typeRefetch}
                />
                <DeleteProductType
                  productType={selectedProductType}
                  refetch={typeRefetch}
                />
              </>
            )}
          </div> */}
        </div>
      </Tabs>

      <div className="flex justify-between items-center">
        <Tabs value={unitFilter} onValueChange={(value) => setUnitFilter(value)}>
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value={'-1'} className="text-base px-2 py-1">
                Toàn bộ
              </TabsTrigger>

              {[
                ...new Set(
                  products
                    .filter((el) => el.productTypeId.toString() == tab)
                    .map((p) => capitalize(p.unit)),
                ),
              ].map((el) => (
                <TabsTrigger key={el} value={el} className="text-base">
                  {el}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </Tabs>

        {/* {tab && (
          <div className="flex justify-between items-center">
            <CreateTransaction refetch={refetch} products={products} productTypes={productTypes} />

            <CreateTransaction
              refetch={refetch}
              productTypes={productTypes}
              products={products}
              transactionType={TransactionType.EXPORT}
              renderButton={
                <Button className="my-4 mx-1" size="sm" variant="default">
                  <Import className="mr-2 h-4 w-4" /> Xuất hàng
                </Button>
              }
            />

            <AddProduct
              productTypes={productTypes}
              refetch={refetch}
              tab={tab}
              unitFilter={unitFilter}
            />
          </div>
        )} */}
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">STT</TableHead>
            {/* <TableHead className="w-[100px]">ID</TableHead> */}
            <TableHead>Tên sản phẩm</TableHead>
            <TableHead>Đơn vị</TableHead>
            <TableHead>Số lượng</TableHead>
            <TableHead>Số lượng cảnh báo</TableHead>
            <TableHead>Loại sản phẩm</TableHead>
            <TableHead className="text-right">Hành động</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products
            .filter((el) => el.productTypeId.toString() == tab)
            .filter((el) => capitalize(el.unit) == unitFilter || unitFilter == '-1')
            .map((el, index: number) => (
              <TableRow key={el.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                {/* <TableCell>{formatID(el.id)}</TableCell> */}
                <TableCell>{el.name}</TableCell>
                <TableCell>{capitalize(el.unit)}</TableCell>
                <TableCell>{el.quantity}</TableCell>
                <TableCell>{el.warningQuantity}</TableCell>
                <TableCell>
                  {productTypes.find((productType) => productType.id == el.productTypeId)?.name ||
                    ''}
                </TableCell>
                <TableCell className="text-right">
                  {/* <EditProduct productTypes={productTypes} product={el} refetch={refetch} />
                  <DeleteProduct refetch={refetch} product={el} /> */}
                  <HistoryProduct refetch={refetch} product={el} />
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </div>
  );
}
