import { ReactNode } from 'react';
import { NavBar } from '@/components/menu-bar';
// import { Sidebar } from "@/components/side-bar";
import './bg.css';

type LayoutProps = {
  children: ReactNode;
};

export function DefaultLayout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen flex flex-col relative">
      {/* Modern navigation bar */}
      <NavBar />

      {/* Main content area with improved spacing and glass effect */}
      <div className="flex-1 flex overflow-hidden">
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6">
            <div className="glass rounded-lg sm:rounded-xl p-4 sm:p-6 transition-normal hover-lift">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* Modern footer */}
      <footer className="glass border-t backdrop-blur-sm">
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4">
          <div className="text-xs sm:text-sm text-muted-foreground text-center">
            © 2024 Pelham CMS - Powered by Modern Design
          </div>
        </div>
      </footer>
    </div>
  );
}
